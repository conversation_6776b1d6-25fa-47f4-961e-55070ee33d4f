ansio==0.0.1
beautifulsoup4==4.12.3
browser-use==0.1.40
docker==7.1.0
duckduckgo-search==6.1.12
faiss-cpu==1.8.0.post1
flask[async]==3.0.3
flask-basicauth==0.2.0
flaredantic==0.1.4
GitPython==3.1.43
inputimeout==1.0.4
langchain-anthropic==0.3.3
langchain-community==0.3.19
langchain-google-genai==2.0.8
langchain-groq==0.2.2
langchain-huggingface==0.1.2
langchain-mistralai==0.2.4
langchain-ollama==0.2.2
langchain-openai==0.3.1
# openai-whisper==20240930  # Temporarily disabled - build issue
lxml_html_clean==0.3.1
markdown==3.7
newspaper3k==0.2.8
paramiko==3.5.0
playwright==1.52.0
pypdf==4.3.1
python-dotenv==1.0.1
pytz==2024.2
sentence-transformers==3.0.1
tiktoken==0.8.0
unstructured
unstructured-client
webcolors==24.6.0
crontab==1.0.1
aiohttp==3.10.11
chromadb==0.5.23
numpy>=1.26.2
redis==5.0.1
psutil==5.9.8

# MCP Dependencies
mcp==1.5.0
fastmcp>=1.0
uv>=0.6.3

# Database Dependencies
sqlalchemy==2.0.23
flask-sqlalchemy==3.1.1
flask-migrate==4.0.5
bcrypt==4.1.2
flask-login==0.6.3
flask-wtf==1.2.1
wtforms==3.1.1

# Testing and Code Quality Dependencies
pytest==8.0.0
pytest-asyncio==0.23.0
pytest-cov==4.0.0
pytest-mock==3.12.0
pytest-xdist==3.5.0
coverage==7.4.0

# Security and Validation Dependencies
cryptography==42.0.0
pydantic==2.10.4
marshmallow==3.20.0
cerberus==1.3.5

# Development Dependencies
black==24.1.0
isort==5.13.0
flake8==7.0.0
mypy==1.8.0
pre-commit==3.6.0
