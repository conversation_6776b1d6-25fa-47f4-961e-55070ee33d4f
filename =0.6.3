Collecting mcp==1.5.0
  Downloading mcp-1.5.0-py3-none-any.whl.metadata (20 kB)
Requirement already satisfied: fastmcp in /opt/miniconda3/lib/python3.13/site-packages (2.7.1)
Collecting uv
  Downloading uv-0.7.17-py3-none-macosx_11_0_arm64.whl.metadata (11 kB)
Requirement already satisfied: anyio>=4.5 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (4.9.0)
Requirement already satisfied: httpx-sse>=0.4 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (0.4.0)
Requirement already satisfied: httpx>=0.27 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (0.28.1)
Requirement already satisfied: pydantic-settings>=2.5.2 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (2.9.1)
Requirement already satisfied: pydantic<3.0.0,>=2.7.2 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (2.11.5)
Requirement already satisfied: sse-starlette>=1.6.1 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (2.3.6)
Requirement already satisfied: starlette>=0.27 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (0.46.2)
Requirement already satisfied: uvicorn>=0.23.1 in /opt/miniconda3/lib/python3.13/site-packages (from mcp==1.5.0) (0.34.3)
Requirement already satisfied: annotated-types>=0.6.0 in /opt/miniconda3/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.5.0) (0.6.0)
Requirement already satisfied: pydantic-core==2.33.2 in /opt/miniconda3/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.5.0) (2.33.2)
Requirement already satisfied: typing-extensions>=4.12.2 in /opt/miniconda3/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.5.0) (4.12.2)
Requirement already satisfied: typing-inspection>=0.4.0 in /opt/miniconda3/lib/python3.13/site-packages (from pydantic<3.0.0,>=2.7.2->mcp==1.5.0) (0.4.1)
Requirement already satisfied: authlib>=1.5.2 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (1.6.0)
Requirement already satisfied: exceptiongroup>=1.2.2 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (1.3.0)
INFO: pip is looking at multiple versions of fastmcp to determine which version is compatible with other requirements. This could take a while.
Collecting fastmcp
  Downloading fastmcp-2.9.2-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.9.1-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.9.0-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.8.1-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.8.0-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.7.0-py3-none-any.whl.metadata (17 kB)
  Downloading fastmcp-2.6.1-py3-none-any.whl.metadata (17 kB)
INFO: pip is still looking at multiple versions of fastmcp to determine which version is compatible with other requirements. This could take a while.
  Downloading fastmcp-2.6.0-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.5.2-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.5.1-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.5.0-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.4.0-py3-none-any.whl.metadata (16 kB)
INFO: This is taking longer than usual. You might need to provide the dependency resolver with stricter constraints to reduce runtime. See https://pip.pypa.io/warnings/backtracking for guidance. If you want to abort this run, press Ctrl + C.
  Downloading fastmcp-2.3.5-py3-none-any.whl.metadata (15 kB)
  Downloading fastmcp-2.3.4-py3-none-any.whl.metadata (15 kB)
  Downloading fastmcp-2.3.3-py3-none-any.whl.metadata (15 kB)
  Downloading fastmcp-2.3.2-py3-none-any.whl.metadata (15 kB)
  Downloading fastmcp-2.3.1-py3-none-any.whl.metadata (15 kB)
  Downloading fastmcp-2.3.0-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.2.10-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.2.9-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.2.8-py3-none-any.whl.metadata (16 kB)
  Downloading fastmcp-2.2.7-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.6-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.5-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.4-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.3-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.2-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.1-py3-none-any.whl.metadata (27 kB)
  Downloading fastmcp-2.2.0-py3-none-any.whl.metadata (27 kB)
Requirement already satisfied: dotenv>=0.9.9 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (0.9.9)
  Downloading fastmcp-2.1.2-py3-none-any.whl.metadata (27 kB)
Requirement already satisfied: fastapi>=0.115.12 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (0.115.12)
  Downloading fastmcp-2.1.1-py3-none-any.whl.metadata (26 kB)
  Downloading fastmcp-2.1.0-py3-none-any.whl.metadata (26 kB)
  Downloading fastmcp-2.0.0-py3-none-any.whl.metadata (26 kB)
  Downloading fastmcp-1.0-py3-none-any.whl.metadata (16 kB)
Requirement already satisfied: python-dotenv>=1.0.1 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (1.1.0)
Requirement already satisfied: typer>=0.9.0 in /opt/miniconda3/lib/python3.13/site-packages (from fastmcp) (0.16.0)
Requirement already satisfied: idna>=2.8 in /opt/miniconda3/lib/python3.13/site-packages (from anyio>=4.5->mcp==1.5.0) (3.7)
Requirement already satisfied: sniffio>=1.1 in /opt/miniconda3/lib/python3.13/site-packages (from anyio>=4.5->mcp==1.5.0) (1.3.1)
Requirement already satisfied: certifi in /opt/miniconda3/lib/python3.13/site-packages (from httpx>=0.27->mcp==1.5.0) (2025.6.15)
Requirement already satisfied: httpcore==1.* in /opt/miniconda3/lib/python3.13/site-packages (from httpx>=0.27->mcp==1.5.0) (1.0.9)
Requirement already satisfied: h11>=0.16 in /opt/miniconda3/lib/python3.13/site-packages (from httpcore==1.*->httpx>=0.27->mcp==1.5.0) (0.16.0)
Requirement already satisfied: click>=8.0.0 in /opt/miniconda3/lib/python3.13/site-packages (from typer>=0.9.0->fastmcp) (8.2.1)
Requirement already satisfied: shellingham>=1.3.0 in /opt/miniconda3/lib/python3.13/site-packages (from typer>=0.9.0->fastmcp) (1.5.4)
Requirement already satisfied: rich>=10.11.0 in /opt/miniconda3/lib/python3.13/site-packages (from typer>=0.9.0->fastmcp) (13.9.4)
Requirement already satisfied: markdown-it-py>=2.2.0 in /opt/miniconda3/lib/python3.13/site-packages (from rich>=10.11.0->typer>=0.9.0->fastmcp) (2.2.0)
Requirement already satisfied: pygments<3.0.0,>=2.13.0 in /opt/miniconda3/lib/python3.13/site-packages (from rich>=10.11.0->typer>=0.9.0->fastmcp) (2.19.1)
Requirement already satisfied: mdurl~=0.1 in /opt/miniconda3/lib/python3.13/site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer>=0.9.0->fastmcp) (0.1.0)
Downloading mcp-1.5.0-py3-none-any.whl (73 kB)
Downloading fastmcp-1.0-py3-none-any.whl (35 kB)
Downloading uv-0.7.17-py3-none-macosx_11_0_arm64.whl (16.3 MB)
   ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.3/16.3 MB 2.5 MB/s eta 0:00:00
Installing collected packages: uv, mcp, fastmcp
  Attempting uninstall: mcp
    Found existing installation: mcp 1.9.3
    Uninstalling mcp-1.9.3:
      Successfully uninstalled mcp-1.9.3
  Attempting uninstall: fastmcp
    Found existing installation: fastmcp 2.7.1
    Uninstalling fastmcp-2.7.1:
      Successfully uninstalled fastmcp-2.7.1

Successfully installed fastmcp-1.0 mcp-1.5.0 uv-0.7.17
