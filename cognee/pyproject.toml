[project]
name = "cognee"
version = "0.1.44"
description = "Cognee - is a library for enriching LLM context with a semantic layer for better understanding and reasoning."
authors = [
    { name = "<PERSON><PERSON><PERSON><PERSON>" },
    { name = "<PERSON>" },
]
requires-python = ">=3.10,<=3.13.2"
readme = "README.md"
license = "Apache-2.0"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Software Development :: Libraries",
    "Operating System :: MacOS :: MacOS X",
    "Operating System :: POSIX :: Linux",
    "Operating System :: Microsoft :: Windows",
]
dependencies = [
    "openai>=1.59.4,<2",
    "python-dotenv>=1.0.1",
    "pydantic==2.10.5",
    "pydantic-settings>=2.2.1,<3",
    "typing_extensions==4.12.2",
    "nltk==3.9.1",
    "numpy>=1.26.4, <=2.1",
    "pandas>=2.2.2",
    # Note: New s3fs and boto3 versions don't work well together
    # Always use comaptible fixed versions of these two dependencies
    "s3fs[boto3]==2025.3.2",
    "sqlalchemy==2.0.39",
    "aiosqlite>=0.20.0,<0.21",
    "tiktoken<=0.9.0",
    "litellm>=1.57.4, <1.71.0",
    "instructor>=1.7.2",
    "langfuse>=2.32.0,<3",
    "filetype>=1.2.0",
    "aiohttp>=3.11.14",
    "aiofiles>=23.2.1",
    "owlready2>=0.47,<0.48",
    "graphistry>=0.33.5,<0.34",
    "pypdf>=4.1.0,<6.0.0",
    "jinja2>=3.1.3,<4",
    "matplotlib>=3.8.3,<4",
    "networkx>=3.4.2,<4",
    "lancedb==0.21.0",
    "alembic>=1.13.3,<2",
    "pre-commit>=4.0.1,<5",
    "scikit-learn>=1.6.1,<2",
    "limits>=4.4.1,<5",
    "fastapi==0.115.7",
    "python-multipart==0.0.20",
    "fastapi-users[sqlalchemy]==14.0.1",
    "dlt[sqlalchemy]>=1.9.0,<2",
    "sentry-sdk[fastapi]>=2.9.0,<3",
    "structlog>=25.2.0,<26",
    "onnxruntime<=1.21.1",
    "pylance==0.22.0",
]

[project.optional-dependencies]
api = [
    "uvicorn==0.34.0",
    "gunicorn>=20.1.0,<21",
]
weaviate = ["weaviate-client==4.9.6"]
qdrant = ["qdrant-client>=1.9.0,<2"]
neo4j = ["neo4j>=5.20.0,<6"]
postgres = [
    "psycopg2>=2.9.10,<3",
    "pgvector>=0.3.5,<0.4",
    "asyncpg==0.30.0",
]
notebook = ["notebook>=7.1.0,<8"]
langchain = [
    "langsmith==0.2.3",
    "langchain_text_splitters==0.3.2",
]
llama-index = ["llama-index-core>=0.12.11,<0.13"]
gemini = ["google-generativeai>=0.8.4,<0.9"]
huggingface = ["transformers>=4.46.3,<5"]
ollama = ["transformers>=4.46.3,<5"]
mistral = ["mistral-common>=1.5.2,<2"]
anthropic = ["anthropic>=0.26.1,<0.27"]
deepeval = ["deepeval>=2.0.1,<3"]
posthog = ["posthog>=3.5.0,<4"]
falkordb = ["falkordb==1.0.9"]
kuzu = ["kuzu==0.8.2"]
groq = ["groq==0.8.0"]
milvus = ["pymilvus>=2.5.0,<3"]
chromadb = [
    "chromadb>=0.3.0,<0.7",
    "pypika==0.48.8",
]
docs = ["unstructured[csv, doc, docx, epub, md, odt, org, ppt, pptx, rst, rtf, tsv, xlsx]>=0.16.13,<0.17"]
codegraph = [
    "fastembed<=0.6.0 ; python_version < '3.13'",
    "transformers>=4.46.3,<5",
    "tree-sitter>=0.24.0,<0.25",
    "tree-sitter-python>=0.23.6,<0.24",
]
evals = [
    "plotly>=6.0.0,<7",
    "gdown>=5.2.0,<6",
]
gui = [
    "pyside6>=6.8.3,<7",
    "qasync>=0.27.1,<0.28",
]
graphiti = ["graphiti-core>=0.7.0,<0.8"]
dev = [
    "pytest>=7.4.0,<8",
    "pytest-cov>=6.1.1",
    "pytest-asyncio>=0.21.1,<0.22",
    "coverage>=7.3.2,<8",
    "mypy>=1.7.1,<2",
    "notebook>=7.1.0,<8",
    "deptry>=0.20.0,<0.21",
    "pylint>=3.0.3,<4",
    "ruff>=0.9.2,<1.0.0",
    "tweepy==4.14.0",
    "gitpython>=3.1.43,<4",
    "mkdocs-material>=9.5.42,<10",
    "mkdocs-minify-plugin>=0.8.0,<0.9",
    "mkdocstrings[python]>=0.26.2,<0.27",
]
debug = ["debugpy==1.8.9"]

[project.urls]
Homepage = "https://www.cognee.ai"
Repository = "https://github.com/topoteretes/cognee"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build]
exclude = [
  "/bin",
  "/dist",
  "/.data",
  "/.github",
  "/alembic",
  "/distributed",
  "/deployment",
  "/cognee-mcp",
  "/cognee-frontend",
  "/examples",
  "/helm",
  "/licenses",
  "/logs",
  "/notebooks",
  "/profiling",
  "/tests",
  "/tools",
]

[tool.ruff]
line-length = 100
exclude = [
    "migrations/",  # Ignore migrations directory
    "notebooks/",       # Ignore notebook files
    "build/",           # Ignore build directory
    "cognee/pipelines.py",
    "cognee/modules/users/models/Group.py",
    "cognee/modules/users/models/ACL.py",
    "cognee/modules/pipelines/models/Task.py",
    "cognee/modules/data/models/Dataset.py"
]

[tool.ruff.lint]
ignore = ["F401"]
