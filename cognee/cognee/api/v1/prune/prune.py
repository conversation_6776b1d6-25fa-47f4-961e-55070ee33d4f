from cognee.modules.users.models import User
from cognee.modules.data.deletion import (
    prune_system as prune_system_function,
    prune_data as prune_data_function,
)

async def prune_data(user: User):
    """Prune all data from the system."""
    # Note: The user parameter is reserved for future use.
    await prune_data_function()


async def prune_system(user: User, graph: bool = True, vector: bool = True, metadata: bool = False):
    """Prune system components."""
    # Note: The user parameter is reserved for future use.
    await prune_system_function(graph, vector, metadata)
