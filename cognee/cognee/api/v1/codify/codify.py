from typing import Optional
import asyncio

from cognee.modules.cognify.config import get_cognify_config
from cognee.modules.pipelines import run_tasks
from cognee.modules.pipelines.tasks.task import Task
from cognee.modules.users.methods import get_default_user
from cognee.modules.users.models import User
from cognee.shared.data_models import KnowledgeGraph
from cognee.tasks.documents import classify_documents, extract_chunks_from_documents
from cognee.modules.data.methods.get_unique_dataset_id import get_unique_dataset_id
from cognee.tasks.graph import extract_graph_from_data
from cognee.tasks.ingestion import ingest_data
from cognee.tasks.repo_processor import get_non_py_files, get_repo_file_dependencies
from cognee.tasks.storage import add_data_points
from cognee.tasks.summarization import summarize_text
from cognee.infrastructure.llm import get_max_chunk_tokens


async def codify(repo_path: str, include_docs: bool = False, user: Optional[User] = None):
    if user is None:
        user = await get_default_user()

    cognee_config = get_cognify_config()
    detailed_extraction = True

    tasks = [
        Task(get_repo_file_dependencies, detailed_extraction=detailed_extraction),
        Task(add_data_points, task_config={"batch_size": 500}),
    ]

    if include_docs:
        non_code_tasks = [
            Task(get_non_py_files, task_config={"batch_size": 50}),
            Task(ingest_data, dataset_name="repo_docs", user=user),
            Task(classify_documents),
            Task(extract_chunks_from_documents, max_chunk_size=get_max_chunk_tokens()),
            Task(
                extract_graph_from_data,
                graph_model=KnowledgeGraph,
                task_config={"batch_size": 50},
            ),
            Task(
                summarize_text,
                summarization_model=cognee_config.summarization_model,
                task_config={"batch_size": 50},
            ),
        ]

        repo_docs_dataset_id = await get_unique_dataset_id("repo_docs", user)
        async for run_status in run_tasks(non_code_tasks, repo_docs_dataset_id, repo_path, user, "cognify_pipeline"):
            yield run_status

    codebase_dataset_id = await get_unique_dataset_id("codebase", user)
    async for run_status in run_tasks(tasks, codebase_dataset_id, repo_path, user, "cognify_code_pipeline"):
        yield run_status
