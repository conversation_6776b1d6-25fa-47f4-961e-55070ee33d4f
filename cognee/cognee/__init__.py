from cognee.version import get_cognee_version

# NOTE: __version__ extraction must be at the top of the __init__.py otherwise
#       there will be circular import issues
__version__ = get_cognee_version()

from .api.v1.add import add
from .api.v1.delete import delete
from .api.v1.cognify import cognify
from .api.v1.codify import codify

from .api.v1.config import (
    system_root_directory,
    data_root_directory,
    monitoring_tool,
    set_classification_model,
    set_summarization_model,
    set_graph_model,
    set_graph_database_provider,
    set_llm_provider,
    set_llm_endpoint,
    set_llm_model,
    set_llm_api_key,
    set_llm_config,
    set_chunk_strategy,
    set_chunk_engine,
    set_chunk_overlap,
    set_chunk_size,
    set_vector_db_provider,
    set_relational_db_config,
    set_migration_db_config,
    set_graph_db_config,
    set_vector_db_config,
    set_vector_db_key,
    set_vector_db_url,
    set_graphistry_config,
)

from .api.v1.datasets import (
    list_datasets,
    discover_datasets,
    list_data,
    get_status,
    delete_dataset,
)

from .api.v1.prune import prune_data, prune_system
from .api.v1.search import SearchType, search
from .api.v1.visualize import visualize_graph, start_visualization_server
from cognee.modules.visualization.cognee_network_visualization import (
    cognee_network_visualization,
)

# Pipelines
from .modules import pipelines

import dotenv

dotenv.load_dotenv(override=True)
