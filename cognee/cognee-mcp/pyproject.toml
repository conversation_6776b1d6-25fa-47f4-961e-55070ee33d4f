[project]
name = "cognee-mcp"
version = "0.3.0"
description = "A MCP server project"
readme = "README.md"
requires-python = ">=3.10"

dependencies = [
    # For local cognee repo usage remove comment bellow and add absolute path to cognee
    #"cognee[postgres,codegraph,gemini,huggingface] @ file:/Users/<USER>/Desktop/cognee",
    "cognee[postgres,codegraph,gemini,huggingface,docs,neo4j]>=0.1.40",
    "fastmcp>=1.0",
    "mcp==1.5.0",
    "uv>=0.6.3",
]

[[project.authors]]
name = "<PERSON>"
email = "<EMAIL>"

[build-system]
requires = [ "hatchling", ]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/cognee_mcp"]

[dependency-groups]
dev = [
    "debugpy>=1.8.12",
]

[tool.hatch.metadata]
allow-direct-references = true

[project.scripts]
cognee = "cognee_mcp.server:main"
