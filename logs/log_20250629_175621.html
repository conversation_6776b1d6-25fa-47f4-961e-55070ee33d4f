<html><body style='background-color:black;font-family: Arial, Helvetica, sans-serif;'><pre>
<br><span style="color: rgb(255, 255, 0); ">Generated new master key. Set SINGULARITY_MASTER_KEY environment variable to persist.</span><br>
<span style=" ">Using default SQLite database: /Volumes/REGEN MAIN SSD/! - APPPS/singularity/nerve/data/nerve.db</span><br>
<span style=" ">Authentication manager initialized</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Database tables created successfully</span><br>
<span style=" ">Running analytics tables migration...</span><br>
<span style=" ">Analytics tables already exist, skipping migration</span><br>
<span style=" ">Database initialized successfully</span><br>
<span style=" ">Initializing framework...</span><br>
<span style=" ">Starting job loop...</span><br>
<span style=" ">Starting server...</span><br>
<br><span style="color: rgb(128, 128, 128); ">Debug: Changing timezone from None to Africa/Johannesburg</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
<br><span style="color: rgb(255, 0, 0); ">Error: JSON serialization error: Object of type Response is not JSON serializable</span><br>
