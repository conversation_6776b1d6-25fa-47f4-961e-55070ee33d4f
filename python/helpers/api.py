import json
import threading
from abc import abstractmethod
from typing import Any, Dict, TypedDict, Union
from dataclasses import asdict, is_dataclass

from flask import Flask, Request, Response

from agent import AgentContext
from initialize import initialize
from python.helpers.errors import format_error
from python.helpers.print_style import PrintStyle
from python.helpers.security.security_middleware import get_security_middleware
from python.helpers.tool import Response as ToolResponse


class ResponseEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles Response objects"""
    def default(self, o):
        if isinstance(o, ToolResponse):
            return o.to_dict()
        if is_dataclass(o) and not isinstance(o, type):
            if hasattr(o, 'to_dict'):
                return o.to_dict()  # type: ignore
            return asdict(o)
        return super().default(o)

Input = dict
Output = Union[Dict[str, Any], Response, TypedDict]  # type: ignore


class ApiHandler:
    def __init__(self, app: Flask, thread_lock: threading.Lock):
        self.app = app
        self.thread_lock = thread_lock

    @classmethod
    def requires_loopback(cls) -> bool:
        return False

    @classmethod
    def requires_api_key(cls) -> bool:
        return False

    @classmethod
    def requires_auth(cls) -> bool:
        return True

    @abstractmethod
    async def process(self, input: Input, request: Request) -> Output:
        pass

    async def handle_request(self, request: Request) -> Response:
        try:
            # Apply security middleware
            security_middleware = get_security_middleware()
            security_response = security_middleware.process_request(request)

            # If security middleware blocks the request, return the response
            if security_response is not None:
                return security_response

            # input data from request based on type
            input_data: Input = {}
            if request.is_json:
                try:
                    if request.data:  # Check if there's any data
                        input_data = request.get_json()
                    # If empty or not valid JSON, use empty dict
                except Exception as e:
                    # Just log the error and continue with empty input
                    PrintStyle().print(f"Error parsing JSON: {str(e)}")
                    input_data = {}
            else:
                input_data = {"data": request.get_data(as_text=True)}

            # process via handler
            output = await self.process(input_data, request)

            # return output based on type
            if isinstance(output, Response):
                response = output
            else:
                try:
                    # Use custom encoder to handle Response objects and dataclasses
                    response_json = json.dumps(output, cls=ResponseEncoder)
                    response = Response(
                        response=response_json, status=200, mimetype="application/json"
                    )
                except TypeError as json_error:
                    # Handle non-serializable objects
                    PrintStyle.error(f"JSON serialization error: {json_error}")
                    error_response = {"error": "Response data is not JSON serializable"}
                    response = Response(
                        response=json.dumps(error_response),
                        status=500,
                        mimetype="application/json",
                    )

            # Apply security headers
            return security_middleware.process_response(response)

            # return exceptions with 500
        except Exception as e:
            error = format_error(e)
            PrintStyle.error(f"API error: {error}")
            return Response(response=error, status=500, mimetype="text/plain")

    # get context to run Nerve Agent in
    def get_context(self, ctxid: str):
        with self.thread_lock:
            if not ctxid:
                first = AgentContext.first()
                if first:
                    return first
                return AgentContext(config=initialize())
            got = AgentContext.get(ctxid)
            if got:
                return got
            return AgentContext(config=initialize(), id=ctxid)
